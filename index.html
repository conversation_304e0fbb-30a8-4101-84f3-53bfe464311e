<!DOCTYPE html>
<html lang="it">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description"
        content="Domina Amazon KDP con KDP GENIUS, il primo tool strategico che ti guida passo dopo passo nel self publishing, dalla visione alla pubblicazione.">
    <meta name="keywords"
        content="KDP GENIUS, self publishing, Amazon KDP, tool self publishing, strategia editoriale, pubblicare su amazon">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">
    <title>KDP GENIUS - Il Tuo Stratega Editoriale per il Self Publishing</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Open+Sans:wght@400;600&display=swap');

        :root {
            --notion-bg: #ffffff;
            --notion-text-primary: #1d1d1f;
            --notion-text-secondary: #86868b;
            --notion-accent-subtle: #f5f5f7;
            --notion-border: #d2d2d7;
            --notion-cta-bg: #0071e3;
            --notion-cta-text: #ffffff;
            --notion-cta-hover-bg: #0077ed;
            --primary-blue-muted: #0071e3;

            --border-radius-subtle: 12px;
            --box-shadow-subtle: 0 4px 8px rgba(0, 0, 0, 0.04);
            --box-shadow-none: none;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.47059;
            color: var(--notion-text-primary);
            background-color: var(--notion-bg);
            margin: 0;
            padding: 0;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            font-weight: 400;
            letter-spacing: -0.022em;
        }

        .container {
            max-width: 960px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Navigation Bar - Notion Style */
        .site-nav {
            background-color: var(--notion-bg);
            padding: 15px 0;
            border-bottom: 1px solid var(--notion-border);
            position: sticky;
            top: 0;
            z-index: 1001;
            /* Ensure nav is above other content but below opened mobile menu overlay */
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            min-height: 50px;
            /* Adjusted min-height for a slightly more compact nav */
        }

        .site-logo img {
            height: 45px;
            /* Adjusted logo height */
            display: block;
        }

        .site-logo {
            text-decoration: none;
            display: flex;
            align-items: center;
            margin-right: 15px;
            /* Add some margin to ensure space from nav links if they wrap early */
        }

        .nav-links {
            display: flex;
            align-items: center;
            /* margin-left: auto; This will be handled by media query for desktop */
        }

        .nav-links .main-nav-links {
            display: flex;
            align-items: center;
            margin-right: 15px;
        }

        .nav-links a {
            font-family: 'Open Sans', sans-serif;
            text-decoration: none;
            color: var(--notion-text-secondary);
            margin-left: 12px;
            /* Slightly reduced margin */
            font-weight: 500;
            font-size: 0.88em;
            /* Slightly reduced font size */
            transition: color 0.2s ease;
            padding: 8px 4px;
            /* Adjusted padding */
            line-height: 1.4;
            display: flex;
            align-items: center;
        }

        .nav-links .main-nav-links a:first-child {
            margin-left: 0;
        }

        .nav-links a:hover {
            color: var(--notion-text-primary);
        }

        .nav-links .nav-cta-button {
            background-color: var(--notion-cta-bg);
            color: var(--notion-cta-text);
            padding: 8px 12px;
            /* Adjusted padding */
            border-radius: var(--border-radius-subtle);
            font-weight: 500;
            font-size: 0.82em;
            /* Adjusted font size */
            margin-left: 12px;
            /* Adjusted margin */
            white-space: nowrap;
            line-height: 1.4;
            display: flex;
            align-items: center;
        }

        .nav-links .nav-cta-button:hover {
            background-color: var(--notion-cta-hover-bg);
            color: var(--notion-cta-text);
        }

        .nav-links .nav-login-link {
            white-space: nowrap;
        }

        /* Mobile Navigation Styles */
        #mobile-nav-checkbox {
            display: none;
        }

        .mobile-nav-toggle-label {
            display: none;
            cursor: pointer;
            padding: 10px;
            margin-left: auto;
            /* Push toggle to the right */
            z-index: 1002;
            position: relative;
        }

        .hamburger-icon {
            display: block;
            width: 22px;
            height: 2px;
            background-color: var(--notion-text-primary);
            position: relative;
            transition: background-color 0.1s 0.2s ease-in-out;
        }

        .hamburger-icon::before,
        .hamburger-icon::after {
            content: '';
            position: absolute;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: var(--notion-text-primary);
            transition: transform 0.2s ease-in-out, top 0.2s ease-in-out 0.2s;
        }

        .hamburger-icon::before {
            top: -6px;
        }

        .hamburger-icon::after {
            top: 6px;
        }

        #mobile-nav-checkbox:checked~.mobile-nav-toggle-label .hamburger-icon {
            background-color: transparent;
        }

        #mobile-nav-checkbox:checked~.mobile-nav-toggle-label .hamburger-icon::before {
            transform: rotate(45deg);
            top: 0;
            transition: top 0.2s ease-in-out, transform 0.2s ease-in-out 0.2s;
        }

        #mobile-nav-checkbox:checked~.mobile-nav-toggle-label .hamburger-icon::after {
            transform: rotate(-45deg);
            top: 0;
            transition: top 0.2s ease-in-out, transform 0.2s ease-in-out 0.2s;
        }

        /* Hero Section */
        header.hero {
            background-color: var(--notion-bg);
            color: var(--notion-text-primary);
            padding: 80px 0;
            /* Reverted to a slightly smaller padding than 100px */
            border-bottom: 1px solid var(--notion-border);
            margin-top: 0;
        }

        .hero-container {
            display: flex;
            align-items: center;
            gap: 40px;
            /* Reverted to original gap */
        }

        .hero-text-content {
            flex: 1;
            text-align: left;
        }

        .hero-image-placeholder {
            flex: 1;
            height: 350px;
            /* Adjusted height */
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .hero-image-placeholder img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            display: block;
        }

        header.hero h1 {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
            font-size: 3.5em;
            font-weight: 600;
            margin-bottom: 0.5em;
            line-height: 1.05;
            color: var(--notion-text-primary);
            letter-spacing: -0.015em;
        }

        header.hero h2 {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
            font-size: 1.375em;
            font-weight: 400;
            max-width: none;
            margin: 0 0 40px 0;
            color: var(--notion-text-secondary);
            line-height: 1.381;
            letter-spacing: -0.01em;
        }

        .hero-cta-button {
            margin-top: 10px;
        }

        /* General Section Styling */
        section {
            padding: 70px 0;
            border-bottom: 1px solid var(--notion-accent-subtle);
        }

        section:last-of-type {
            border-bottom: none;
        }

        .section-title {
            text-align: center;
            font-family: 'Montserrat', sans-serif;
            font-size: 2em;
            font-weight: 600;
            margin-bottom: 50px;
            color: var(--notion-text-primary);
        }

        /* Challenges Section */
        .challenges-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .challenge-card {
            background-color: var(--notion-accent-subtle);
            padding: 25px;
            border-radius: var(--border-radius-subtle);
            border: 1px solid var(--notion-border);
        }

        .challenge-card h3 {
            font-family: 'Montserrat', sans-serif;
            font-size: 1.15em;
            font-weight: 600;
            color: var(--notion-text-primary);
            margin-top: 0;
            margin-bottom: 10px;
        }

        .challenge-card p {
            font-size: 0.95em;
            color: var(--notion-text-secondary);
            line-height: 1.6;
            margin-bottom: 0;
        }

        /* Steps Grid */
        .steps-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
        }

        .step-card {
            background: var(--notion-bg);
            padding: 25px;
            border-radius: var(--border-radius-subtle);
            border: 1px solid var(--notion-border);
            display: flex;
            flex-direction: column;
            transition: border-color 0.2s ease;
        }

        .step-card:hover {
            border-color: var(--primary-blue-muted);
            transform: none;
            box-shadow: var(--box-shadow-none);
        }

        .step-card h3 {
            font-family: 'Montserrat', sans-serif;
            color: var(--notion-text-primary);
            margin-top: 0;
            font-size: 1.25em;
            font-weight: 600;
            margin-bottom: 12px;
        }

        .step-card p {
            font-size: 0.9em;
            color: var(--notion-text-secondary);
            flex-grow: 1;
            margin-bottom: 15px;
        }

        .step-card .benefit {
            font-style: normal;
            color: var(--notion-text-secondary);
            padding-top: 15px;
            margin-top: auto;
            border-top: 1px solid var(--notion-accent-subtle);
            font-size: 0.85em;
        }

        .step-card .benefit strong {
            font-weight: 600;
            color: var(--notion-text-primary);
        }

        .new-badge {
            background-color: var(--notion-accent-subtle);
            color: var(--notion-text-secondary);
            font-size: 0.7em;
            padding: 3px 8px;
            border-radius: var(--border-radius-subtle);
            font-weight: 500;
            display: inline-block;
            margin-left: 8px;
            vertical-align: middle;
            border: 1px solid var(--notion-border);
        }

        .usp-list {
            list-style: none;
            padding-left: 0;
        }

        .usp-list li {
            padding-left: 25px;
            margin-bottom: 15px;
            font-size: 1em;
            position: relative;
            color: var(--notion-text-primary);
        }

        .usp-list li::before {
            content: '•';
            color: var(--notion-text-secondary);
            position: absolute;
            left: 5px;
            top: 1px;
            font-size: 1.2em;
        }

        .cta-button {
            display: inline-block;
            background-color: var(--notion-cta-bg);
            color: var(--notion-cta-text);
            padding: 16px 32px;
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
            font-size: 1.0625em;
            font-weight: 400;
            text-decoration: none;
            border-radius: var(--border-radius-subtle);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.6, 1);
            box-shadow: var(--box-shadow-subtle);
            text-align: center;
            border: none;
            letter-spacing: -0.01em;
        }

        .cta-button:hover {
            background-color: var(--notion-cta-hover-bg);
            transform: translateY(-1px);
            box-shadow: 0 8px 16px rgba(0, 113, 227, 0.15);
        }

        .cta-section {
            text-align: center;
            background: var(--notion-accent-subtle);
            color: var(--notion-text-primary);
            padding-top: 70px;
            padding-bottom: 70px;
        }

        .cta-section h2 {
            font-size: 1.8em;
            font-weight: 600;
            color: var(--notion-text-primary);
        }

        .cta-section p {
            color: var(--notion-text-secondary);
            font-size: 1em;
            max-width: 650px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.6;
        }

        .placeholder {
            background-color: var(--notion-bg);
            border: 1px dashed var(--notion-border);
            padding: 20px;
            border-radius: var(--border-radius-subtle);
            text-align: center;
            color: var(--notion-text-secondary);
            margin-top: 20px;
            font-size: 0.9em;
        }

        .placeholder p {
            font-size: inherit;
            color: inherit;
        }

        .cta-section .placeholder {
            background-color: var(--notion-bg);
        }

        .faq dt {
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
            font-size: 1.15em;
            margin-top: 20px;
            color: var(--notion-text-primary);
        }

        .faq dd {
            margin-left: 0;
            margin-bottom: 20px;
            color: var(--notion-text-secondary);
            font-size: 0.95em;
            line-height: 1.6;
        }

        footer.cta-section {
            background: var(--notion-bg);
            border-top: 1px solid var(--notion-border);
            padding-top: 50px;
            padding-bottom: 50px;
        }

        footer.cta-section .cta-button {
            margin-top: 15px;
            margin-bottom: 30px;
        }

        footer.cta-section p {
            font-size: 0.9em;
            color: var(--notion-text-secondary);
        }

        footer.cta-section p:last-child {
            margin-top: 30px;
            font-size: 0.85em;
            opacity: 1;
        }

        /* Pricing Section Styles */
        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin-top: 40px;
            margin-bottom: 40px;
        }

        .pricing-card {
            background-color: var(--notion-bg);
            border: 1px solid var(--notion-border);
            padding: 25px;
            border-radius: var(--border-radius-subtle);
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .pricing-card>div:first-of-type {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }

        .pricing-card.popular {
            border: 2px solid var(--primary-blue-muted);
            position: relative;
        }

        .pricing-card .popular-badge-container {
            min-height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
        }

        .pricing-card .popular-badge {
            background-color: var(--primary-blue-muted);
            color: var(--notion-cta-text);
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: 600;
            display: inline-block;
            line-height: 1.2;
            margin-bottom: 0;
        }

        .pricing-card .empty-badge-placeholder {
            display: block;
            height: 22px;
        }

        .pricing-card h3 {
            font-family: 'Montserrat', sans-serif;
            font-size: 1.25em;
            font-weight: 600;
            color: var(--notion-text-primary);
            margin-top: 0;
            margin-bottom: 10px;
        }

        .pricing-card .bonus-text {
            color: #28a745;
            font-size: 0.9em;
            font-weight: 500;
            margin-bottom: 10px;
            min-height: 1.35em;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .pricing-card .bonus-text.empty {
            visibility: hidden;
        }

        .pricing-card .price {
            font-family: 'Montserrat', sans-serif;
            font-size: 2em;
            font-weight: 600;
            color: var(--notion-text-primary);
            margin-bottom: 5px;
        }

        .pricing-card .price-per-credit {
            color: var(--notion-text-secondary);
            font-size: 0.85em;
            margin-bottom: 20px;
        }

        .pricing-card .cta-button {

            margin-top: 15px;
        }

        .pricing-card .cta-button.primary {
            background-color: var(--notion-cta-bg);
            color: var(--notion-cta-text);
        }

        .pricing-card .cta-button.primary:hover {
            background-color: var(--notion-cta-hover-bg);
        }

        .pricing-card .cta-button.secondary {
            background-color: var(--notion-accent-subtle);
            color: var(--notion-text-primary);
            border: 1px solid var(--notion-border);
        }

        .pricing-card .cta-button.secondary:hover {
            background-color: var(--notion-border);
        }

        /* Media Queries for Navigation and Hero */
        @media (max-width: 768px) {
            .mobile-nav-toggle-label {
                display: inline-flex;
                align-items: center;
            }

            .nav-links {
                display: none;
            }

            .nav-container {
                justify-content: space-between;
            }

            .site-logo {
                margin-right: 0;
            }

            /* Hero Section Mobile Styles */
            header.hero {
                padding: 40px 0;
                /* Further reduced padding for mobile */
            }

            .hero-container {
                flex-direction: column;
                gap: 20px;
                /* Reduced gap */
                text-align: center;
            }

            .hero-text-content {
                order: 2;
                /* Text below image on mobile */
                width: 100%;
            }

            header.hero h1 {
                font-size: 2em;
                /* Adjusted h1 font size for mobile */
                margin-bottom: 0.4em;
            }

            header.hero h2 {
                font-size: 1em;
                /* Adjusted h2 font size for mobile */
                margin-bottom: 20px;
            }

            .hero-image-placeholder {
                order: 1;
                /* Image above text on mobile */
                width: 100%;
                max-width: 280px;
                /* Constrain image width a bit more */
                height: auto;
                margin: 0 auto 20px auto;
                /* Center and add margin below image */
            }

            .hero-image-placeholder img {
                width: 100%;
                height: auto;
            }

            #mobile-nav-checkbox:checked~.nav-links {
                display: flex !important;
                flex-direction: column;
                position: absolute;
                top: 100%;
                /* Position below the nav bar */
                left: 0;
                right: 0;
                background-color: var(--notion-bg);
                border-top: 1px solid var(--notion-border);
                border-bottom: 1px solid var(--notion-border);
                padding: 10px 0;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
                z-index: 1000;
            }

            #mobile-nav-checkbox:checked~.nav-links .main-nav-links {
                flex-direction: column;
                width: 100%;
                margin-right: 0;
            }

            #mobile-nav-checkbox:checked~.nav-links .main-nav-links a,
            #mobile-nav-checkbox:checked~.nav-links .nav-login-link,
            #mobile-nav-checkbox:checked~.nav-links .nav-cta-button {
                width: 100%;
                text-align: center;
                margin-left: 0;
                padding: 12px 20px;
                border-bottom: 1px solid var(--notion-accent-subtle);
                box-sizing: border-box;
            }

            #mobile-nav-checkbox:checked~.nav-links .main-nav-links a:last-of-type {
                border-bottom: 1px solid var(--notion-accent-subtle);
            }

            #mobile-nav-checkbox:checked~.nav-links .nav-login-link {
                border-bottom: 1px solid var(--notion-accent-subtle);
            }

            #mobile-nav-checkbox:checked~.nav-links .nav-cta-button {
                border-bottom: none;
            }
        }

        @media (min-width: 769px) {
            .mobile-nav-toggle-label {
                display: none !important;
            }

            .nav-links {
                display: flex !important;
                flex-direction: row !important;
                position: static !important;
                background-color: transparent !important;
                border-top: none !important;
                border-bottom: none !important;
                padding: 0 !important;
                box-shadow: none !important;
                margin-left: auto !important;
            }

            .nav-links .main-nav-links {
                flex-direction: row !important;
                width: auto !important;
                margin-right: 15px !important;
            }

            .nav-links .main-nav-links a,
            .nav-links .nav-login-link {
                width: auto !important;
                text-align: left !important;
                margin-left: 12px !important;
                padding: 8px 4px !important;
                border-bottom: none !important;
                font-size: 0.88em !important;
            }

            .nav-links .main-nav-links a:first-child {
                margin-left: 0 !important;
            }

            .nav-links .nav-cta-button {
                width: auto !important;
                text-align: center !important;
                margin-left: 12px !important;
                padding: 8px 12px !important;
                /* Adjusted padding to match general link font size better */
                border-bottom: none !important;
                font-size: 0.82em !important;
            }
        }

        /* Responsive for Mac Mockup */
        @media (max-width: 1100px) {
            .mac-mockup {
                width: 800px !important;
                height: 480px !important;
            }

            .mac-screen {
                width: 720px !important;
                height: 440px !important;
                left: 40px !important;
            }
        }

        @media (max-width: 900px) {
            .mac-mockup {
                width: 600px !important;
                height: 360px !important;
            }

            .mac-screen {
                width: 540px !important;
                height: 320px !important;
                left: 30px !important;
            }
        }

        @media (max-width: 700px) {
            .mac-mockup {
                width: 400px !important;
                height: 240px !important;
            }

            .mac-screen {
                width: 360px !important;
                height: 200px !important;
                left: 20px !important;
            }

            .mac-content {
                padding: 20px !important;
            }

            .mac-content h3 {
                font-size: 16px !important;
            }

            .mac-stats {
                grid-template-columns: 1fr !important;
                gap: 10px !important;
            }

            .mac-stat {
                padding: 15px !important;
            }

            .mac-stat-value {
                font-size: 20px !important;
            }

            .mac-covers {
                gap: 8px !important;
            }

            .mac-cover {
                width: 40px !important;
                height: 60px !important;
            }
        }
    </style>
    <script>
        // Get the hash from the URL
        const hash = window.location.hash.substring(1);

        if (hash) {
            // Parse hash parameters
            const params = new URLSearchParams(hash);

            // Check if this is a recovery flow
            if (params.get('type') === 'recovery' && params.get('access_token')) {
                // Redirect to the password update page with query parameters
                const newUrl = '/auth_update_password?' + hash;
                window.location.replace(newUrl);
            } else if (params.get('access_token')) {
                // Regular auth flow - redirect to main app with query parameters
                const newUrl = '/?' + hash;
                window.location.replace(newUrl);
            } else {
                // Show error
                document.getElementById('error-message').style.display = 'block';
                document.getElementById('error-message').textContent = 'Invalid authentication parameters.';
                setTimeout(() => {
                    window.location.replace('/auth_login');
                }, 3000);
            }
        } else {
            // No hash parameters - show error and redirect
            document.getElementById('error-message').style.display = 'block';
            document.getElementById('error-message').textContent = 'No authentication parameters found.';
            setTimeout(() => {
                window.location.replace('/auth_login');
            }, 3000);
        }
    </script>

    <script>
        // Savings Calculator Functionality
        function updateCalculator() {
            const hourlyRate = parseFloat(document.getElementById('hourlyRate').value) || 30;
            const booksPerMonth = parseInt(document.getElementById('booksSlider').value) || 1;

            // Update display values
            document.getElementById('booksCount').textContent = booksPerMonth;
            document.getElementById('displayRate').textContent = hourlyRate;
            document.getElementById('displayBooks').textContent = booksPerMonth;

            // Calculate savings (80 hours per book)
            const hoursPerMonth = 80 * booksPerMonth;
            const monthlySavings = hourlyRate * hoursPerMonth;

            // Update results
            document.getElementById('hoursPerMonth').textContent = hoursPerMonth;
            document.getElementById('monthlySavings').textContent = '€' + monthlySavings.toLocaleString('it-IT');
            document.getElementById('displayTotal').textContent = monthlySavings.toLocaleString('it-IT');
        }

        // Initialize calculator when page loads
        document.addEventListener('DOMContentLoaded', function () {
            const hourlyRateInput = document.getElementById('hourlyRate');
            const booksSlider = document.getElementById('booksSlider');

            if (hourlyRateInput && booksSlider) {
                hourlyRateInput.addEventListener('input', updateCalculator);
                booksSlider.addEventListener('input', updateCalculator);

                // Initial calculation
                updateCalculator();
            }
        });
    </script>
</head>

<body>

    <nav class="site-nav">
        <div class="container nav-container">
            <a href="https://kdpgenius.com" class="site-logo"><img src="kdp.png" alt="KDP GENIUS Logo"></a>

            <input type="checkbox" id="mobile-nav-checkbox">
            <label for="mobile-nav-checkbox" class="mobile-nav-toggle-label">
                <span class="hamburger-icon"></span>
            </label>

            <div class="nav-links">
                <div class="main-nav-links">
                    <a href="#problema">Sfide</a>
                    <a href="#soluzione">Soluzione</a>
                    <a href="#come-funziona">Come Funziona</a>
                    <a href="#usp">Perché Noi</a>
                    <a href="#focus-cover">Cover</a>
                    <a href="#social-proof">Testimonianze</a>
                    <a href="#offerta">Offerta</a>
                    <a href="#faq">FAQ</a>
                </div>
                <a href="https://app.kdpgenius.com/" class=" nav-login-link">Login</a>
                <a href="https://app.kdpgenius.com/auth_signup" class="cta-button nav-cta-button">Get Started</a>
            </div>
        </div>
    </nav>

    <header class="hero">
        <div class="container hero-container">
            <div class="hero-text-content">
                <h1>Domina Amazon KDP. KDP GENIUS: Il Tuo Stratega Editoriale.</h1>
                <h2>Strategia completa per il self-publishing: identità d'autore, nicchie, cover, descrizioni.
                    Costruisci il tuo successo su KDP.</h2>
                <a href="#offerta" class="cta-button hero-cta-button">Inizia Ora la Tua Strategia</a>
            </div>
            <div class="hero-image-placeholder">
                <img src="kdp-hero.png" alt="KDP Genius Abstract Graphic">
            </div>
        </div>
    </header>

    <main>
        <!-- Apple-style Mockup Section -->
        <section style="padding: 100px 0; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);">
            <div class="container" style="text-align: center;">
                <div style="max-width: 900px; margin: 0 auto;">
                    <!-- Mac Mockup Placeholder -->
                    <div style="position: relative; display: inline-block; margin-bottom: 60px;">
                        <!-- MacBook Base -->
                        <div class="mac-mockup"
                            style="width: 1000px; height: 600px; background: linear-gradient(145deg, #e8e8e8, #f5f5f5); border-radius: 20px; position: relative; box-shadow: 0 25px 50px rgba(0,0,0,0.15);">
                            <!-- MacBook Screen -->
                            <div class="mac-screen"
                                style="width: 900px; height: 560px; background: #000000; border-radius: 15px; position: absolute; top: 20px; left: 50px; overflow: hidden; border: 8px solid #2c2c2c;">
                                <!-- Screen Content -->
                                <div
                                    style="width: 100%; height: 100%; background: #ffffff; position: relative; overflow: hidden;">
                                    <!-- Browser Window -->
                                    <div
                                        style="height: 40px; background: #f6f6f6; border-bottom: 1px solid #e5e5e5; display: flex; align-items: center; padding: 0 16px;">
                                        <div style="display: flex; gap: 8px;">
                                            <div
                                                style="width: 12px; height: 12px; background: #ff5f57; border-radius: 50%;">
                                            </div>
                                            <div
                                                style="width: 12px; height: 12px; background: #ffbd2e; border-radius: 50%;">
                                            </div>
                                            <div
                                                style="width: 12px; height: 12px; background: #28ca42; border-radius: 50%;">
                                            </div>
                                        </div>
                                        <div style="flex: 1; text-align: center; font-size: 13px; color: #666;">
                                            kdpgenius.com</div>
                                    </div>
                                    <!-- Content Area -->
                                    <div style="padding: 40px; text-align: left;">
                                        <h3
                                            style="font-size: 24px; font-weight: 600; margin-bottom: 16px; color: #1d1d1f;">
                                            KDP GENIUS Dashboard</h3>
                                        <div
                                            style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px;">
                                            <div style="background: #f5f5f7; padding: 20px; border-radius: 8px;">
                                                <div style="font-size: 14px; color: #86868b; margin-bottom: 8px;">Tempo
                                                    Risparmiato</div>
                                                <div style="font-size: 28px; font-weight: 600; color: #0071e3;">80 ore
                                                </div>
                                            </div>
                                            <div style="background: #f5f5f7; padding: 20px; border-radius: 8px;">
                                                <div style="font-size: 14px; color: #86868b; margin-bottom: 8px;">Libri
                                                    Creati</div>
                                                <div style="font-size: 28px; font-weight: 600; color: #34c759;">12</div>
                                            </div>
                                        </div>
                                        <!-- Fake ebook covers -->
                                        <div style="display: flex; gap: 12px;">
                                            <div
                                                style="width: 60px; height: 80px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 4px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                                            </div>
                                            <div
                                                style="width: 60px; height: 80px; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border-radius: 4px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                                            </div>
                                            <div
                                                style="width: 60px; height: 80px; background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border-radius: 4px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                                            </div>
                                            <div
                                                style="width: 60px; height: 80px; background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); border-radius: 4px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <h2
                            style="font-size: 2.5em; font-weight: 600; margin-bottom: 20px; color: #1d1d1f; letter-spacing: -0.015em;">
                            Tutto in un'unica piattaforma</h2>
                        <p
                            style="font-size: 1.25em; color: #86868b; max-width: 600px; margin: 0 auto; line-height: 1.4;">
                            Dashboard professionale per gestire tutti i tuoi progetti editoriali con la precisione di un
                            sistema Apple.</p>
                    </div>
                </div>
        </section>
        <section id="problema" class="challenges">
            <div class="container">
                <h2 class="section-title">Sei un autore self-publisher e ti riconosci in queste sfide?</h2>
                <div class="challenges-grid">
                    <div class="challenge-card">
                        <h3>Il blocco dello stratega</h3>
                        <p>Hai tante idee, ma fatichi a definire un'identità editoriale chiara e una direzione precisa
                            per le tue pubblicazioni?</p>
                    </div>
                    <div class="challenge-card">
                        <h3>La giungla delle keyword</h3>
                        <p>Passi ore a cercare keyword e nicchie su Amazon sperando di intercettare i lettori giusti,
                            con risultati spesso deludenti?</p>
                    </div>
                    <div class="challenge-card">
                        <h3>L'incubo della pagina prodotto</h3>
                        <p>Titoli poco efficaci, descrizioni che non convincono, copertine anonime... e le vendite non
                            decollano?</p>
                    </div>
                    <div class="challenge-card">
                        <h3>La paura di sbagliare</h3>
                        <p>Timore di violare policy di KDP, usare termini vietati o semplicemente di investire tempo e
                            risorse in un libro che nessuno leggerà?</p>
                    </div>
                    <div class="challenge-card">
                        <h3>La mancanza di un piano</h3>
                        <p>Pubblichi libri in modo sporadico, senza una visione d'insieme o una strategia per costruire
                            una vera carriera d'autore?</p>
                    </div>
                </div>
                <p style="text-align: center; font-size: 1.1em; margin-top: 40px; color: var(--notion-text-secondary);">
                    Se hai annuito leggendo, sappi che non sei solo. Il self publishing può sembrare un percorso
                    complesso e
                    solitario. <strong>Ma cosa succederebbe se avessi al tuo fianco un copilota esperto, uno stratega
                        dedicato che ti guida in ogni singola fase del processo?</strong></p>
            </div>
        </section>

        <section id="soluzione">
            <div class="container" style="text-align:center;">
                <h2 class="section-title">Ecco KDP GENIUS</h2>
                <h3
                    style="font-family: 'Montserrat', sans-serif; font-weight: 500; font-size: 1.4em; color: var(--notion-text-primary); margin-bottom: 15px;">
                    Il Tool Strategico per il Self Publishing che Trasforma la Tua Passione in un Business Editoriale di
                    Successo.</h3>
                <p
                    style="font-size: 1em; color: var(--notion-text-secondary); max-width: 700px; margin: 0 auto 15px auto; line-height: 1.6;">
                    KDP GENIUS non è l'ennesimo generatore di contenuti. È il primo strumento completo pensato per
                    accompagnare l'autore self-publisher lungo tutto il processo strategico di pubblicazione. Il nostro
                    obiettivo? Aiutarti a costruire una vera e propria identità editoriale coerente con ciò che il
                    mercato cerca realmente, ottimizzando ogni aspetto per massimizzare la tua visibilità e le tue
                    vendite su Amazon.</p>
                <p
                    style="font-size: 1em; color: var(--notion-text-secondary); max-width: 700px; margin: 0 auto; line-height: 1.6;">
                    <strong>Dalla visione iniziale al file pronto per la pubblicazione, KDP GENIUS è al tuo
                        fianco.</strong>
                </p>
            </div>
        </section>

        <section id="come-funziona">
            <div class="container">
                <h2 class="section-title">Un Percorso Guidato in 12 Step</h2>
                <div class="steps-grid">
                    <div class="step-card">
                        <h3>STEP 1 - Home + Mindset</h3>
                        <p>Inizia con il piede giusto. Un'introduzione ispirazionale per orientarti: "Non stai scrivendo
                            un libro, stai costruendo un'identità editoriale." Definisci subito la tua visione, che tu
                            parta da zero o abbia già delle idee.</p>
                        <p class="benefit"><strong>Beneficio:</strong> Chiarezza e motivazione fin dal primo giorno, per
                            un approccio strategico e consapevole.</p>
                    </div>
                    <div class="step-card">
                        <h3>STEP 2 - Vision Builder</h3>
                        <p>Chi sei come autore? Cosa vuoi costruire? Per chi? Attraverso domande guidate, KDP GENIUS ti
                            aiuta a definire la tua identità, il tono e gli obiettivi editoriali, creando un profilo
                            autore solido.</p>
                        <p class="benefit"><strong>Beneficio:</strong> Una bussola precisa per tutte le tue future
                            scelte editoriali, assicurando coerenza e riconoscibilità.</p>
                    </div>
                    <div class="step-card">
                        <h3>STEP 3 - Keyword & Niche Analyzer</h3>
                        <p>Dimentica le congetture. Il tool cerca in tempo reale gli argomenti e le keyword realmente
                            digitate dal tuo pubblico su Amazon, Google e altri motori. Visualizza volumi di ricerca,
                            livelli di difficoltà e le opportunità nascoste.</p>
                        <p class="benefit"><strong>Beneficio:</strong> Pubblica libri che il mercato desidera,
                            posizionati per le keyword giuste e scopri nicchie profittevoli con dati reali.</p>
                    </div>
                    <div class="step-card">
                        <h3>STEP 4 - Idea Analyzer</h3>
                        <p>Ogni idea libro viene analizzata per originalità, rilevanza per il pubblico e potenziali
                            rischi (es. parole vietate, claim medici). Una guida per fare scelte editoriali consapevoli
                            e sicure.</p>
                        <p class="benefit"><strong>Beneficio:</strong> Riduci il rischio di insuccesso e problemi con le
                            policy, investendo nelle idee con il maggior potenziale.</p>
                    </div>
                    <div class="step-card">
                        <h3>STEP 5 - Catalog Map</h3>
                        <p>Trasforma le tue idee in una strategia a lungo termine. Crea una mini-roadmap editoriale, una
                            collana logica e coerente. KDP GENIUS ti suggerisce l'ordine ideale tra più titoli e
                            tematiche.</p>
                        <p class="benefit"><strong>Beneficio:</strong> Costruisci un catalogo di libri che si supportano
                            a vicenda, fidelizzando i lettori e aumentando il valore nel tempo.</p>
                    </div>
                    <div class="step-card">
                        <h3>STEP 6 - Buyer Persona Generator</h3>
                        <p>Conosci il tuo lettore ideale come le tue tasche. Genera un profilo dettagliato: età,
                            problemi, stile di vita, aspettative. Questo guiderà ogni tua decisione successiva.</p>
                        <p class="benefit"><strong>Beneficio:</strong> Scrivi libri che parlano direttamente al cuore (e
                            ai bisogni) del tuo pubblico, aumentando l'engagement e le vendite.</p>
                    </div>
                    <div class="step-card">
                        <h3>STEP 7 - Titolo + Sottotitolo</h3>
                        <p>Non lasciare al caso l'elemento più importante della tua pagina prodotto. KDP GENIUS propone
                            titoli e sottotitoli ad alta conversione, basati sulle keyword selezionate e sul tono della
                            tua buyer persona. Personalizzabili al 100%.</p>
                        <p class="benefit"><strong>Beneficio:</strong> Cattura l'attenzione immediata dei lettori giusti
                            e aumenta drasticamente i click-through rate.</p>
                    </div>
                    <div class="step-card">
                        <h3>STEP 8 - Indice Perfetto</h3>
                        <p>Struttura il tuo libro per il massimo impatto. Il tool genera un indice multilivello (Parte >
                            Capitolo > Sotto-capitolo) e include suggerimenti per esercizi, ricette, rimedi pratici, a
                            seconda del genere.</p>
                        <p class="benefit"><strong>Beneficio:</strong> Offri ai tuoi lettori un'esperienza di lettura
                            fluida, organizzata e di grande valore.</p>
                    </div>
                    <div class="step-card">
                        <h3>STEP 9 - Cover Perfetta <span class="new-badge">NOVITÀ!</span></h3>
                        <p>La prima impressione è tutto. Basandosi su ricerche, vision e buyer persona, KDP GENIUS
                            genera automaticamente 4 cover visive professionali e coerenti con il contenuto, tra cui
                            scegliere. Pronte da usare o da rifinire.</p>
                        <p class="benefit"><strong>Beneficio:</strong> Ottieni copertine accattivanti che convertono,
                            senza bisogno di competenze grafiche o costosi designer.</p>
                    </div>
                    <div class="step-card">
                        <h3>STEP 10 - Descrizione + Bio + Email</h3>
                        <p>Dalla teoria alla pratica del marketing. Genera descrizioni Amazon persuasive (con struttura
                            AIDA), una bio autore ottimizzata per il tuo target e contenuti pronti per email di lancio o
                            post social.</p>
                        <p class="benefit"><strong>Beneficio:</strong> Risparmia ore di lavoro e ottieni testi di
                            vendita efficaci, pronti per promuovere il tuo libro.</p>
                    </div>
                    <div class="step-card">
                        <h3>STEP 11 - Legal Center</h3>
                        <p>Pubblica in totale tranquillità. KDP GENIUS controlla che titolo e sottotitolo non violino
                            marchi registrati, termini vietati da KDP o policy critiche, segnalandoti subito i rischi.
                        </p>
                        <p class="benefit"><strong>Beneficio:</strong> Evita costosi errori, rifiuti da parte di Amazon
                            e proteggi la tua reputazione di autore.</p>
                    </div>
                    <div class="step-card">
                        <h3>STEP 12 - Esportazione Finale</h3>
                        <p>Tutto ciò che ti serve, pronto all'uso. Esporta tutti gli output (buyer persona, indice,
                            titoli, descrizione, copertina) in formato PDF e Word. Pronti da inviare a Plotly, a
                            un'agenzia o per caricare direttamente su KDP.</p>
                        <p class="benefit"><strong>Beneficio:</strong> Semplifica il processo di pubblicazione e hai
                            sempre a portata di mano tutti gli elementi strategici del tuo libro.</p>
                    </div>
                </div>
            </div>
        </section>

        <section id="usp">
            <div class="container">
                <h2 class="section-title">KDP GENIUS è Diverso: Non Solo Contenuti, Ma Strategia Editoriale Completa.
                </h2>
                <p
                    style="text-align: center; max-width: 700px; margin: 0 auto 30px auto; font-size: 1em; color: var(--notion-text-secondary); line-height: 1.6;">
                    A differenza di altri tool che si concentrano solo su singoli aspetti, KDP GENIUS abbraccia l'intero
                    ecosistema del self-publishing. La nostra filosofia è semplice: "Non sei qui per scrivere qualcosa.
                    Sei qui per costruire qualcosa di tuo." Ti forniamo gli strumenti e la metodologia per:</p>
                <ul class="usp-list" style="max-width: 650px; margin: 0 auto;">
                    <li><strong>Costruire un'Identità Editoriale Forte e Coerente:</strong> Non solo libri, ma un brand
                        d'autore.</li>
                    <li><strong>Basare le Tue Scelte su Dati Reali:</strong> Minimizzi i rischi e massimizzi le
                        opportunità.</li>
                    <li><strong>Ottimizzare Ogni Elemento per la Vendita:</strong> Dal titolo alla descrizione, dalla
                        cover alla strategia di catalogo.</li>
                    <li><strong>Risparmiare Tempo Prezioso:</strong> Automatizzando processi complessi ma cruciali.</li>
                    <li><strong>Avere il Pieno Controllo Strategico:</strong> Con output chiari e pronti all'uso.</li>
                </ul>
            </div>
        </section>

        <section id="focus-cover" style="padding: 120px 0; background: #000000; color: #ffffff;">
            <div class="container" style="text-align: center;">
                <h2
                    style="font-size: 3em; font-weight: 600; margin-bottom: 30px; color: #ffffff; letter-spacing: -0.015em;">
                    Cover che Vendono</h2>
                <p
                    style="font-size: 1.375em; color: #a1a1a6; max-width: 700px; margin: 0 auto 80px auto; line-height: 1.4;">
                    Design professionali generati automaticamente. Nessuna competenza grafica richiesta.
                </p>

                <!-- Ebook Covers Showcase -->
                <div style="display: flex; justify-content: center; gap: 40px; margin-bottom: 60px; flex-wrap: wrap;">
                    <!-- Cover 1 -->
                    <div style="position: relative; transform: rotate(-5deg);">
                        <div
                            style="width: 200px; height: 300px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 8px; box-shadow: 0 20px 40px rgba(0,0,0,0.3); position: relative; overflow: hidden;">
                            <div style="position: absolute; top: 30px; left: 20px; right: 20px;">
                                <div
                                    style="font-size: 18px; font-weight: 700; color: white; line-height: 1.2; margin-bottom: 20px;">
                                    GUIDA COMPLETA AL MARKETING</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.8);">Strategie Vincenti per il
                                    2024</div>
                            </div>
                            <div style="position: absolute; bottom: 20px; left: 20px; right: 20px;">
                                <div style="font-size: 14px; font-weight: 500; color: white;">Marco Rossi</div>
                            </div>
                        </div>
                    </div>

                    <!-- Cover 2 -->
                    <div style="position: relative; transform: rotate(2deg);">
                        <div
                            style="width: 200px; height: 300px; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border-radius: 8px; box-shadow: 0 20px 40px rgba(0,0,0,0.3); position: relative; overflow: hidden;">
                            <div style="position: absolute; top: 30px; left: 20px; right: 20px;">
                                <div
                                    style="font-size: 18px; font-weight: 700; color: white; line-height: 1.2; margin-bottom: 20px;">
                                    MINDFULNESS E BENESSERE</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.8);">La Via della Serenità</div>
                            </div>
                            <div style="position: absolute; bottom: 20px; left: 20px; right: 20px;">
                                <div style="font-size: 14px; font-weight: 500; color: white;">Laura Bianchi</div>
                            </div>
                        </div>
                    </div>

                    <!-- Cover 3 -->
                    <div style="position: relative; transform: rotate(-3deg);">
                        <div
                            style="width: 200px; height: 300px; background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border-radius: 8px; box-shadow: 0 20px 40px rgba(0,0,0,0.3); position: relative; overflow: hidden;">
                            <div style="position: absolute; top: 30px; left: 20px; right: 20px;">
                                <div
                                    style="font-size: 18px; font-weight: 700; color: white; line-height: 1.2; margin-bottom: 20px;">
                                    INVESTIMENTI SMART</div>
                                <div style="font-size: 12px; color: rgba(255,255,255,0.8);">Costruisci la Tua Ricchezza
                                </div>
                            </div>
                            <div style="position: absolute; bottom: 20px; left: 20px; right: 20px;">
                                <div style="font-size: 14px; font-weight: 500; color: white;">Andrea Verdi</div>
                            </div>
                        </div>
                    </div>
                </div>

                <p style="font-size: 1.125em; color: #a1a1a6; max-width: 600px; margin: 0 auto;">
                    4 proposte professionali generate automaticamente per ogni libro. Scegli, personalizza e pubblica.
                </p>
            </div>
        </section>

        <section id="social-proof">
            <div class="container">
                <h2 class="section-title">Cosa Dicono gli Autori che Usano KDP GENIUS</h2>
                <div class="placeholder">
                    <p><strong>[Placeholder per Testimonianze Reali]</strong></p>
                    <p><em>"[Es. Ho sempre faticato a trovare le keyword giuste, ma con KDP GENIUS ho scoperto nicchie
                            che non avrei mai immaginato! Le mie vendite sono aumentate del X%]" - Nome Autore,
                            Genere</em></p>
                    <p><em>"[Es. La funzione Cover Perfetta è geniale! Ho risparmiato centinaia di euro in grafici e
                            finalmente ho copertine che vendono.]" - Nome Autore, Genere</em></p>
                    <p><em>"[Es. KDP GENIUS mi ha dato la struttura e la strategia che mi mancavano. Ora mi sento un
                            vero imprenditore editoriale.]" - Nome Autore, Genere</em></p>
                </div>
            </div>
        </section>

        <!-- Savings Calculator Section -->
        <section id="calculator"
            style="padding: 120px 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
            <div class="container" style="text-align: center;">
                <h2
                    style="font-size: 3em; font-weight: 600; margin-bottom: 30px; color: #ffffff; letter-spacing: -0.015em;">
                    Calcola il Tuo Risparmio
                </h2>
                <p
                    style="font-size: 1.375em; color: rgba(255,255,255,0.8); max-width: 700px; margin: 0 auto 60px auto; line-height: 1.4;">
                    Scopri quanto tempo e denaro risparmi ogni mese con KDP GENIUS
                </p>

                <div
                    style="max-width: 600px; margin: 0 auto; background: rgba(255,255,255,0.1); backdrop-filter: blur(20px); border-radius: 20px; padding: 40px; box-shadow: 0 20px 40px rgba(0,0,0,0.1);">
                    <!-- Hourly Rate Input -->
                    <div style="margin-bottom: 40px;">
                        <label
                            style="display: block; font-size: 1.125em; font-weight: 500; margin-bottom: 15px; color: white;">
                            Il tuo costo orario (€)
                        </label>
                        <input type="number" id="hourlyRate" value="30" min="10" max="200"
                            style="width: 100%; padding: 16px; font-size: 1.25em; text-align: center; border: none; border-radius: 12px; background: rgba(255,255,255,0.9); color: #1d1d1f; font-weight: 600;">
                    </div>

                    <!-- Books per Month Slider -->
                    <div style="margin-bottom: 40px;">
                        <label
                            style="display: block; font-size: 1.125em; font-weight: 500; margin-bottom: 15px; color: white;">
                            Libri che scrivi al mese: <span id="booksCount" style="font-weight: 700;">1</span>
                        </label>
                        <input type="range" id="booksSlider" min="1" max="10" value="1"
                            style="width: 100%; height: 8px; border-radius: 4px; background: rgba(255,255,255,0.3); outline: none; -webkit-appearance: none;">
                        <style>
                            #booksSlider::-webkit-slider-thumb {
                                -webkit-appearance: none;
                                appearance: none;
                                width: 24px;
                                height: 24px;
                                border-radius: 50%;
                                background: #ffffff;
                                cursor: pointer;
                                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                            }

                            #booksSlider::-moz-range-thumb {
                                width: 24px;
                                height: 24px;
                                border-radius: 50%;
                                background: #ffffff;
                                cursor: pointer;
                                border: none;
                                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                            }
                        </style>
                    </div>

                    <!-- Results -->
                    <div
                        style="background: rgba(255,255,255,0.15); border-radius: 16px; padding: 30px; margin-bottom: 30px;">
                        <div style="font-size: 1.125em; color: rgba(255,255,255,0.8); margin-bottom: 10px;">
                            Risparmio mensile
                        </div>
                        <div id="monthlySavings"
                            style="font-size: 3.5em; font-weight: 700; color: #ffffff; margin-bottom: 10px;">
                            €2,400
                        </div>
                        <div style="font-size: 1em; color: rgba(255,255,255,0.7);">
                            <span id="hoursPerMonth">80</span> ore risparmiate al mese
                        </div>
                    </div>

                    <div style="font-size: 1em; color: rgba(255,255,255,0.8); line-height: 1.5;">
                        <strong>Formula:</strong> 80 ore × €<span id="displayRate">30</span> × <span
                            id="displayBooks">1</span> libro/mese = €<span id="displayTotal">2,400</span>
                    </div>
                </div>
            </div>
        </section>

        <section id="offerta" class="cta-section">
            <div class="container">
                <h2 class="section-title">Pronto a Trasformare la Tua Carriera di Autore Self-Publisher?</h2>
                <p>Scegli il piano KDP GENIUS più adatto a te e inizia subito a costruire il tuo successo editoriale.
                </p>

                <div class="pricing-grid">
                    <!-- Card 1: 50 Credits -->
                    <div class="pricing-card">
                        <div>
                            <div class="popular-badge-container">
                                <span class="empty-badge-placeholder"></span> <!-- Placeholder for alignment -->
                            </div>
                            <h3>50 Credits</h3>
                            <p class="bonus-text empty">&nbsp;</p>
                            <h2 class="price">€17.99</h2>
                            <p class="price-per-credit">€0.360 per credit</p>
                        </div>
                        <a href="https://app.kdpgenius.com/auth_signup" class="cta-button secondary">Compra 50
                            Crediti</a>
                    </div>

                    <!-- Card 2: 100 Credits (Popular) -->
                    <div class="pricing-card popular">
                        <div>
                            <div class="popular-badge-container">
                                <span class="popular-badge">POPULAR</span>
                            </div>
                            <h3>100 Credits</h3>
                            <p class="bonus-text">+50 Bonus Credits!</p>
                            <h2 class="price">€39.99</h2>
                            <p class="price-per-credit">€0.267 per credit</p>
                        </div>
                        <a href="https://app.kdpgenius.com/auth_signup" class="cta-button primary">Compra 150
                            Crediti</a>
                    </div>

                    <!-- Card 3: 200 Credits -->
                    <div class="pricing-card">
                        <div>
                            <div class="popular-badge-container">
                                <span class="empty-badge-placeholder"></span> <!-- Placeholder for alignment -->
                            </div>
                            <h3>200 Credits</h3>
                            <p class="bonus-text">+100 Bonus Credits!</p>
                            <h2 class="price">€69.99</h2>
                            <p class="price-per-credit">€0.233 per credit</p>
                        </div>
                        <a href="https://app.kdpgenius.com/auth_signup" class="cta-button secondary">Compra 300
                            Crediti</a>
                    </div>
                </div>

                <a href="https://app.kdpgenius.com/auth_signup" class="cta-button" style="margin-top: 30px;">Sì, Voglio
                    Costruire la Mia Identità Editoriale e Vendere Più Libri!</a>
                <div class="placeholder"
                    style="margin-top: 25px; background-color: var(--notion-bg); border: 1px dashed var(--notion-border);">
                    <p><strong>[Placeholder Garanzia - SE APPLICABILE]</strong><br>
                        Prova KDP GENIUS Senza Rischi! Soddisfatto o Rimborsato per X Giorni</p>
                </div>
            </div>
        </section>

        <section id="faq">
            <div class="container">
                <h2 class="section-title">Domande Frequenti (FAQ)</h2>
                <dl class="faq">
                    <dt>D: KDP GENIUS scrive i libri al posto mio?</dt>
                    <dd>R: No, KDP GENIUS è uno strumento strategico. Non si limita a generare contenuti, ma ti guida
                        nel costruire una solida identità editoriale e nel prendere decisioni informate su cosa scrivere
                        e come posizionarlo sul mercato. Ti aiuta a creare la struttura, i metadati e i materiali di
                        marketing, ma la creatività della scrittura resta tua.</dd>

                    <dt>D: Cosa ottengo concretamente con l'Esportazione Finale?</dt>
                    <dd>R: Riceverai documenti in formato PDF e Word contenenti: il profilo dettagliato della tua buyer
                        persona, l'indice strutturato del tuo libro, proposte di titoli e sottotitoli, la descrizione
                        Amazon persuasiva, la tua bio autore e la copertina scelta. Tutto pronto per la pubblicazione o
                        per ulteriori lavorazioni.</dd>

                    <dt>D: È difficile da usare? Serve essere esperti di tecnologia?</dt>
                    <dd>R: KDP GENIUS è progettato per essere intuitivo e guidarti passo dopo passo. L'obiettivo è
                        semplificare la complessità, non aggiungerla.</dd>

                    <dt>D: Per quali generi di libri è adatto KDP GENIUS?</dt>
                    <dd>R: KDP GENIUS è versatile e può essere utilizzato da autori di saggistica, manualistica,
                        self-help, e anche per la pianificazione strategica di romanzi (identità autore, target,
                        posizionamento). Alcuni tool, come quello per l'indice, possono avere suggerimenti più specifici
                        per la non-fiction.</dd>
                </dl>
            </div>
        </section>

    </main>

    <footer class="cta-section">
        <div class="container">
            <h2 class="section-title" style="font-size: 1.6em;">Non lasciare che le tue idee restino nel cassetto.</h2>
            <p>È il momento di passare da "scrittore" a "imprenditore editoriale di successo".<br><strong>KDP GENIUS è
                    lo stratega che ti serve.</strong></p>
            <br>
            <a href="https://app.kdpgenius.com/auth_signup" class="cta-button">Accedi Subito a KDP GENIUS e Inizia a
                Costruire il
                Tuo Futuro!</a>
            <p>&copy; 2025 KDP GENIUS - Tutti i diritti riservati.</p>
        </div>
    </footer>

</body>

</html>